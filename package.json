{"name": "playwright-<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"testauthprod": "cross-env ENV=prod playwright test --project=test-prod-chrome-auth", "testpostprod": "cross-env ENV=prod playwright test --project=test-prod-chrome-post --headed", "testsurveyprod": "cross-env ENV=prod playwright test --project=test-prod-chrome-survey", "testauthstaging": "cross-env ENV=staging playwright test --project=test-staging-chrome-auth", "testpoststaging": "cross-env ENV=staging playwright test --project=test-staging-chrome-post", "testsurveystaging": "cross-env ENV=staging playwright test --project=test-staging-chrome-survey", "testallprod": "cross-env ENV=prod playwright test --workers=1 --project=test-prod-chrome-post --project=test-prod-chrome-auth  --project=test-prod-chrome-survey", "testtaskprod": "cross-env ENV=prod playwright test --workers=1 --project=test-prod-chrome-task --headed --ui", "testtaskprod1": "cross-env ENV=prod playwright test --workers=1 --project=test-prod-chrome-task "}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@playwright/test": "^1.53.2", "@types/node": "^24.0.3", "dotenv": "^17.2.1", "npm-run-all": "^4.1.5"}, "dependencies": {"cross-env": "^7.0.3"}}