import { Page, expect } from "@playwright/test";

export class TaskVerify {
  constructor(private page: Page) {}
  //verify task ở cvct
  async verifyMyTaskVisible(nameMyTask: string) {
    await expect(this.page.getByRole('link', { name: '<PERSON><PERSON>ng việc của tôi' })).toBeVisible();
    await expect(this.page.locator('span.task-name-text', { hasText: nameMyTask })).toBeVisible();
  }
  async verifyMyTaskNotExist(nameMyTask: string) {
    await expect(this.page.locator('span.task-name-text', { hasText: nameMyTask })).toHaveCount(0);
  }
  //verify dự án chung
  async verifyProjectVisible(projectName: string) {
    await expect(this.page.getByText(projectName)).toBeVisible();
  }
  async verifyCollabProjectVisible(collabProjectName: string) { 
    await expect(this.page.locator('span.task-tree-node-text', { hasText: collabProjectName })).toBeVisible();
  }
  async verifyCollabProjecttNotExist(collabProjectName: string) { 
    await expect(this.page.locator('span.task-tree-node-text', { hasText: collabProjectName })).toHaveCount(0);
  }
  async verifyEditProjectVisible(newProjectName: string) {
    await expect(this.page.locator('span.task-tree-node-text', { hasText: newProjectName })).toBeVisible();
  }
  async verifyProjectNotExist(newProjectName: string,projectName: string) {
    await expect(this.page.locator('span.task-tree-node-text', { hasText: newProjectName })).toHaveCount(0);
    await expect(this.page.locator('span.task-tree-node-text', { hasText: projectName })).toHaveCount(0);
  }
  async verifyArchived(){
    await expect(this.page.locator("div", { hasText: "Lưu trữ dự án thành công" })
  ).toBeVisible({ timeout: 5000 });
  await this.page.getByRole('button', { name: 'Đóng' }).click();

  }
  //verify thêm thành viên vào dự án
  async verifyAddMemberVisible(projectName: string) {
    const memberCheckbox = this.page.locator('label:has(input[type="checkbox"].spectrum-Checkbox-input_4870fc) input[type="checkbox"]').first();
    const memberName = await memberCheckbox.locator('xpath=..').textContent();
    await expect(memberCheckbox).toBeChecked({ timeout: 5000 });
  }
    //verify danh sách công viec
  async verifyTaskListVisible(nameTaskList1: string) {
    await this.page.locator('div.task-tree-node-wrapper').first().click();
    await expect(this.page.locator('span.task-tree-node-text', { hasText: nameTaskList1 })).toBeVisible();
  }

  //verify thư mục
  async verifyFolderVisible(nameFolder1: string) {
    await this.page.locator('div.task-tree-node-wrapper').first().click();
    await expect(this.page.locator('span.task-tree-node-text', { hasText: nameFolder1 })).toBeVisible();
  }
  //verify tạo bản sao 
  async verifyDuplicateTaskListVisible(nameTaskList1: string) {
    await this.page.locator('div.task-tree-node-wrapper').first().click();
    const dup = `Bản sao của ${nameTaskList1}`;
    await expect(this.page.locator('span.task-tree-node-text', { hasText: dup })).toBeVisible();
  }
  async verifyDuplicateFolderVisible(nameFolder1: string) {
    await this.page.locator('div.task-tree-node-wrapper').first().click();
    const dup = `Bản sao của ${nameFolder1}`;
    await expect(this.page.locator('span.task-tree-node-text', { hasText: dup })).toBeVisible();
  }
  async verifyDuplicateTaskVisible(nameTask: string) {
    const dup =`Bản sao của ${nameTask}`;
    await expect(this.page.locator('span.task-name-text', { hasText: dup })).toBeVisible({timeout: 20000});
  }

  //verify task 
  async verifyTaskVisible(nameTask: string) {
    await expect(this.page.locator('span.task-name-text', { hasText: nameTask})).toBeVisible({timeout: 20000});
  }
  async verifyNewTaskVisible(newNameTask: string) {
    await expect(this.page.locator('span.task-name-text', { hasText: newNameTask})).toBeVisible({timeout: 30000});
  }
}
