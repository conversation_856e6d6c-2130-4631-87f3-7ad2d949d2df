import { Page, expect } from "@playwright/test";

export class TaskAction {
  constructor(private page: Page) {}
  //mytask
  async clickMyTask() {
    await this.page.getByRole("link", { name: "<PERSON>ông việc của tôi" }).click();
  }

  async clickCreateTaskBtn() {
    await this.page.getByRole("button", { name: "Tạo", exact: true }).click();
  }

  async fillTaskTitle(nameMyTask: string) {
    await this.page.getByRole("textbox", { name: "Nhập tiêu đề công việc..." }).fill(nameMyTask);
  }

  async clickSaveBtn() {
    await this.page.getByRole("button", { name: "<PERSON><PERSON><PERSON>" }).click();
    await this.page.waitForTimeout(3000);
  }

  // async clickArchivedBtn() {
  //   await this.page.getByRole("button", { name: "<PERSON><PERSON><PERSON>" }).click();
  //   await this.page.waitForTimeout(3000);
  // }

  async listTaskByMyTask(nameMyTask: string) {
    return this.page.locator("span.task-name-text", { hasText: nameMyTask }).first();
  }

  async clickThreeDotMenu() {
    await this.page.locator("button.menu-task__action").first().click();
  }

  async clickDeleteBtn() {
    await this.page.getByText("Xóa").click({ timeout: 3000 });
    const deleteBtn = this.page.getByRole("button", { name: "Xóa" });
    await deleteBtn.waitFor({ state: "visible", timeout: 5000 });
    await deleteBtn.click();
  }
  //btn xoa
  async clickDeleteProjectBtn() {
    await this.page.waitForTimeout(500);
    await this.page.getByText('Xóa').click({ timeout: 5000 });
    await this.page.getByRole('button', { name: 'Xóa' }).click({ timeout: 5000 });
  }

  async clickDuplicateBtn() {
    await this.page.getByText("Tạo bản sao").click({ timeout: 500 });
  }

  async clickViewDetailsBtn() {
    await this.page.getByText("Xem chi tiết").click({ timeout: 500 });
  }

  //project
  async clickIconProject1(){ 
    await this.page.waitForTimeout(500);
    await this.page.locator('button[class*="task-sidebar"]').click();
  }

  //icon + từng project //danh sách công việc
  async clickIconProject2(){
    await this.page.locator("a.btn-option-add").first().click({ timeout: 5000 });
  }

  //chọn dscv or folder
  async clickChooseTaskListOrFolder() {
    await this.page.locator('//span[contains(@class, "ant-dropdown")]');
  }

  async clickChooseTaskList() {
    await this.page.locator('//span[text()="Thêm danh sách công việc"]').click();
  }

  async clickChooseFolder() {
    await this.page.locator('//span[text()="Thêm thư mục"]').click();
  }

  //fill ten project placeholder
  projectName() {
    return this.page.locator('input[placeholder="Đặt tên cho dự án..."]');
  }
  async clickProjectName(projectName: string) {
    await this.page.waitForTimeout(500);
    await this.projectName().type(projectName);
    await this.page.waitForTimeout(500);
  }
  async fillNewProjectName(newProjectName: string) {
    await this.projectName().fill(newProjectName);
  }

  async addMemberBtn() {
    await this.page.locator("button.btn.btn-add-member-project").click();
  }

  async chooseMemberToProject() {
    const member = this.page.locator('label:has(input[type="checkbox"].spectrum-Checkbox-input_4870fc)').first();
    const memberName = await member.textContent();
    await member.locator('input[type="checkbox"]').check();
  }

  async closeProjectModal() {
    await this.page.locator('//button[contains(@class, "styles_closeButton__QjmHU")]').click({timeout : 5000});
  }

  async clickDoneButton() {
    await this.page.getByRole("button", { name: "Xong" }).click();
  }

  async clickSaveProject() {
    await this.page.locator('//div[contains(@class, "modal-dialog")]//button[(text()="Lưu")]').click();
    await this.page.waitForTimeout(3000);
  }

  async clickEnterKey() {
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(500);
  }

  async clickEnterKeyTwice() {
    await this.page.waitForTimeout(1000);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(500);
    await this.page.keyboard.press("Enter");
    await this.page.waitForTimeout(500);
  }

  //ba cham cuoi mỗi project
  optionBtn() {
    return this.page.locator("button.btn-option-edit");
  }
  //ba cham cuoi project
  async clickProjectOptionBtn() {
    await this.page.waitForTimeout(1000);
    await this.optionBtn().first().click({ timeout: 5000 });
  }
  //ba chấm cuối mỗi tasklist or folder
  async clickTaskListOrFolderOptionBtn(nameTaskList1: string)  {
    const wrapper = this.page.locator(`.task-tree-node-wrapper:has(span.task-tree-node-text:has-text("${nameTaskList1}"))`);
    const optionBtn = wrapper.locator("button.btn-option-edit");
    await wrapper.hover();
    await optionBtn.waitFor({ state: "visible", timeout: 5000 });
    await optionBtn.click();
  }
  //ba cham cuoi collab project
  async clickCollabProjectOption(collabProjectName: string) {
    await this.page.waitForTimeout(1000);
    const wrapper = this.page.locator(`.task-tree-node-wrapper:has(span.task-tree-node-text:has-text("${collabProjectName}"))`);
    const optionBtn = wrapper.locator("button.btn-option-edit");
    await wrapper.hover();
    await optionBtn.waitFor({ state: "visible", timeout: 5000 });
    await optionBtn.click();
  }
   //chọn project
  async chooseProject(projectName: string, newProjectName: string){
    let project = this.page.locator("div.task-tree-node-wrapper");
    // nếu có tên project, tìm kiếm theo tên
    if (projectName) {
      const target = project.filter({ hasText: projectName });
      if ((await target.count()) > 0) {
        await target.first().hover();
        await target.first().click();
        return;
      }
    }
    // nếu không tìm thấy project theo tên, kiểm tra tên mới (tên sau edit)
    if (newProjectName) {
      const target = project.filter({ hasText: newProjectName });
      if ((await target.count()) > 0) {
        await target.first().hover();
        await target.first().click();
        return;
      }
    }
    // fallback: chọn project đầu tiên
      const firstProject = project.first();
      await firstProject.hover();
      await firstProject.click();
  }

  //chọn action chỉnh sửa
  async clickActionEdit() {
    await this.page.locator('div.gapo-Typography', { hasText: "Chỉnh sửa dự án" }).click({ timeout: 10000 });
  }

  //chọn action thêm thành viên vào dự án
  async clickActionAdd() {
    await this.page.locator("div.gapo-Typography", { hasText: "Thêm thành viên vào dự án" }).click({ timeout: 8000 });
  }

  //chọn action ẩn dự án
  async clickActionnHideProject() {
    await this.page.locator('//div[contains(@class,"gapo-Typography") and normalize-space(text())="Ẩn dự án"]').click({ force: true });
  }

  //chọn action luu trữ
  async clickActionArchived(){
    await this.page.locator("div.gapo-Typography", { hasText: "Lưu trữ dự án" }).click({ force: true, timeout: 5000 });
  }
  
  //chọn action tạo bản sao
  async clickActionDuplicate() {
    await this.page.waitForTimeout(1000);
    const duplicateItem = this.page.locator('li[role="menuitem"][data-key="DUPLICATE"]');
    await duplicateItem.click();
  }
  // add task list
  async addTaskList1(nameTaskList1 : string)   {
    await this.page.locator('//input[@placeholder="Đặt tên cho danh sách công việc..."]').fill(nameTaskList1);
    await this.page.waitForTimeout(500);
  }

  //thêm folder
  async addFolder(nameFolder1: string){
    await this.page.locator('//input[@placeholder="Đặt tên cho thư mục..."]').fill(nameFolder1);
    await this.page.waitForTimeout(500);
  }

  async chooseTaskList(nameTaskList1: string) {
    const taskList = this.page.locator("div.task-tree-node-wrapper"); // locator của task list
    // nếu có tên task list, tìm kiếm theo tên
    if (nameTaskList1) {
      const target = taskList.filter({ hasText: nameTaskList1 });
      if ((await target.count()) > 0) {
        await target.first().hover();
        await target.first().click();
        return;
      }
    }
    // fallback: chọn task list đầu tiên
    const firstTaskList = taskList.nth(1);
    await firstTaskList.hover();
    await firstTaskList.click();
  }
 
  //Chọn task cần edit
  async chooseTask(nameTask: string)  {
    await this.page.locator(".task-name-text__main", { hasText: nameTask }).hover();
  }
  
  //chọn đúng task list
  async clickTaskList(nameTaskList1: string) {
    await this.page.locator("span.task-tree-node-text", { hasText: nameTaskList1 }).click();
  }
  
  //create new task or creat new section // ấn btn Tạo có 2 loại tạo cv mới or tạo thư mục
  async clickCreateNewTaskBtn() {
    await this.page.getByText("Tạo công việc mới").click();
  }
 
  //chat
  async clickCreatGroupChat()  {
    await this.page.locator('button[class*="spectrum-ActionButton--quiet"]:has(svg[fill="var(--spectrum-global-color-accentWorkSecondary)"])').click();
    await this.page.getByRole("menuitem", { name: "Tạo nhóm Chat Tạo nhóm Chat v" }).locator("div").nth(1).click({ timeout: 500 });
  }
  async sellectUser() {
    await this.page.locator('input[type="checkbox"].spectrum-Checkbox-input_4870fc').nth(0).check();
  }
  async clickDoneChat() {
    await this.page.getByRole("button", { name: "Xong" }).click();
  }
  async tabTask() {
    await this.page.getByLabel("collab-tab").getByText("Công việc").click();
  }
  async enableTaskCollab() {
    await this.page.getByRole("button", { name: "Kích hoạt chức năng công việc" }).click();
    await this.page.getByRole("button", { name: "Kích hoạt" }).click();
  }
  async showAProject() {
    await this.page.locator(".gapo-ActionButton.icon-header").first().click();
    await this.page.locator("input.spectrum-ToggleSwitch-input_3526dd").click();
  }
  async clickCollabProject(collabProjectName: string) {
    const project = this.page.locator("div.task-tree-node-wrapper").filter({ hasText: collabProjectName });
    if ((await project.count()) > 0) {
      await project.first().click();
    } else {
      console.log("Không tìm thấy dự án collab -> Failed ẩn dự án ");
    }
  }
}
