import { Page, expect } from "@playwright/test";

export class SurveyVerify {
  constructor(private page: Page) {}

  async verifySurveyVisible() {
    await expect(this.page.getByRole('button', { name: '<PERSON><PERSON> khảo sát' })).toBeVisible();
  }

  async verifySurveyDeleted() {
    // Tuỳ UI, có thể check theo message "Không có khảo sát"
    await expect(this.page.getByText('Không có khảo sát')).toBeVisible();
  }
}
