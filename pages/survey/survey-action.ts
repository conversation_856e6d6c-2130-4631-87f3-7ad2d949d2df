import { Page } from "@playwright/test";

export class SurveyAction {
  constructor(private page: Page) {}

  // Main survey creation methods
  async createSurveyNoDeadline(participants: string[], title: string) {
    await this.clickCreateSurveyButton();
    await this.selectTextQuestionType();
    await this.addParticipants(participants);
    await this.inputSurveyName(title);
    await this.sendSurvey();
  }

  async createSurveyWithDeadline(
    deadlineDate: string,
    participants: string[],
    title: string
  ) {
    await this.clickCreateSurveyButton();
    await this.setDeadline(deadlineDate);
    await this.selectTextQuestionType();
    await this.addParticipants(participants);
    await this.inputSurveyName(title);
    await this.page.waitForTimeout(3000);
    await this.sendSurvey();
    await this.page.waitForTimeout(3000);
  }

  // Basic action methods
  private async clickCreateSurveyButton() {
    await this.page.getByRole("button", { name: "Tạo khảo sát" }).click();
  }

  private async selectTextQuestionType() {
    await this.page.getByRole("button", { name: "Đoạn văn bản" }).click();
  }

  private async setDeadline(deadlineDate: string) {
    await this.page.locator("#DeadlineWrapper div").nth(2).click();
    await this.page.getByTitle(deadlineDate).locator("div").click();
    await this.page.getByRole("button", { name: "Hoàn thành" }).click();
  }

  private async addParticipants(participants: string[]) {
    await this.page.getByRole("button", { name: "Thêm người" }).click();
    for (const participant of participants) {
      await this.page.getByText(participant).click();
    }
    await this.page.getByRole("button", { name: "Xong" }).click();
  }

  private async sendSurvey() {
    await this.page.getByRole("button", { name: "Gửi khảo sát" }).click();
  }

  private async inputSurveyName(title: string) {
    await this.page.waitForTimeout(1000);
    const titleInput = this.page.getByRole("textbox").first();
    await titleInput.fill("");
    await titleInput.fill(title);
  }

  async closeSuccessPopup() {
    await this.clickCloseButton();
    await this.page.waitForTimeout(2000);
  }

  async createRecurringSurvey(participants: string[], title: string) {
    await this.clickCreateSurveyButton();
    await this.selectRecurringType();
    await this.editRemindToNoRemind();
    await this.selectTextQuestionType();
    await this.addParticipants(participants);
    await this.inputSurveyName(title);
    await this.sendSurvey();
  }

  // Recurring survey specific methods
  private async selectRecurringType() {
    await this.page.getByRole("radio", { name: "Định kỳ" }).check();
  }

  private async clickCloseButton() {
    await this.page.getByRole("button", { name: "Đóng" }).click();
  }

  async editFirstSurveyType(newType: string) {
    await this.openFirstSurveyMenu();
    await this.clickEditOption();
    await this.changeSurveyType(newType);
    await this.saveEditedSurvey();
  }

  async editRemindToNoRemind() {
    await this.page.waitForTimeout(1000);
    await this.openReminderDropdown();
    await this.selectNoReminder();
  }

  async deleteFirstSurvey() {
    await this.openFirstSurveyMenu();
    await this.clickDeleteOption();
    await this.confirmDeletion();
  }

  // Survey editing helper methods
  private async openFirstSurveyMenu() {
    await this.page.locator('//button[@aria-haspopup="true"]').first().click();
  }

  private async clickEditOption() {
    await this.page.getByText("Chỉnh sửa", { exact: true }).click();
  }

  private async changeSurveyType(newType: string) {
    await this.page.getByRole("button", { name: "Hàng ngày" }).click();
    await this.page.getByText(newType).click();
  }

  private async saveEditedSurvey() {
    await this.page.getByRole("button", { name: "Sửa khảo sát" }).click();
  }

  private async openReminderDropdown() {
    await this.page.getByRole('button', { name: 'Nhắc trả lời trước 30 phút' }).click();
  }

  private async selectNoReminder() {
    await this.page.getByText('Không nhắc trước').click();
  }

  private async clickDeleteOption() {
    await this.page
      .getByRole("menuitem", { name: "Xóa" })
      .locator("div")
      .first()
      .click();
  }

  private async confirmDeletion() {
    await this.page.getByRole("button", { name: "Xác nhận" }).click();
  }

  async cleanUpSurveys() {
    // Implementation for cleaning up surveys if needed
  }
}
