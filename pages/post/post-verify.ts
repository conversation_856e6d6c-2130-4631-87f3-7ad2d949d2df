import { Page } from "@playwright/test";
import { expect } from "@playwright/test";

export class PostVerify {

  constructor(private page: Page) {}

  //verify
  async verifyPostContent(postContent: string): Promise<void> {
    await expect(this.page.getByRole("main")).toContainText(postContent);
  }

  //verify post
  async verifyPostMediaNumber(expectedCount: number): Promise<void> {
    const list = this.page.locator(
      'xpath=//div[@class="post-item"][1]//div[contains(@class,"attachment__item")]'
    );
    await expect(list).toHaveCount(expectedCount);
  }

  //verify background
  async verifyBackground(): Promise<void> {
    const backgroundImage = await this.page
      .locator(
        'xpath=//div[@class="post-item"][1]//div[contains(@class, "post-item__content")]'
      )
      .evaluate((el) => getComputedStyle(el).backgroundImage);
    await expect(backgroundImage).toContain(
      "fb009ca1-d40d-4722-a082-6b097f60768e.png"
    );
  }
  //verify tag user
  async verifyTagUserList(postTagNumber: number): Promise<void> {
    await expect(
      this.page.getByText(`${postTagNumber - 1} người khác`)
    ).toBeVisible();
  }
  //verify post mention
  async verifyMentionPost(): Promise<void> {
    await expect(
      this.page.getByTestId("popover").getByText("Gapo Auto Test 03")
    ).toBeVisible();

    await expect(
      this.page.getByRole("button", { name: "<EMAIL>" })
    ).toBeVisible();

    await expect(this.page.getByRole("button", { name: "+" })).toBeVisible();
  }

  //verify post askme
  async verifyPostAskMe(userName: string): Promise<void> {
    const container = this.page.locator("div.sc-bxivhb.dwOkQB").first();
    await expect(container).toContainText("Đặt câu hỏi cho " + userName);

    await expect(
      this.page.getByRole("button", { name: "Hỏi ngay" }).first()
    ).toBeVisible();
  }

  //verify post askme
  async verifyPostPollvote(
    pollvoteQuestion: string,
    voteOpitons: string[]
  ): Promise<void> {
    //verify pollvote question
    await expect(
      this.page.locator(
        '//div[@class="post-item"][1]//div[@class="post-item__list-poll-vote"]/div/h3'
      )
    ).toContainText(pollvoteQuestion);
    //verify vote option
    for (const title of voteOpitons) {
      await expect(
        this.page.locator(
          `//div[@class="post-item"][1]//h3[@class="vote-title" and text()="${title}"]`
        )
      ).toBeVisible();
    }
  }
}
