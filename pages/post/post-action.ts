import { Page } from "@playwright/test";
import { NavigateAction } from "../navigate/navigate-action";

export class PostAction {

  constructor(private page: Page) {}

  //open post area
  async clickCreatePostBtn(): Promise<void> {
    const nav = new NavigateAction(this.page);

    await this.page
      .locator("div")
      .filter({ hasText: /^Bạn muốn chia sẻ điều gì\?$/ })
      .first()
      .click();
    await this.page.waitForTimeout(500);

    //if the confirm popup dislay, accept yes
    await nav.acceptNotiPopup;
    await nav.continueGuidelineOfPost();
  }
  //input post content
  async inputPostContent(postContent: string): Promise<void> {
    await this.page.getByRole("textbox").fill(postContent);
  }
  //upload media post
  async uploadMediaToPost(filePaths: string[]): Promise<void> {
    await this.page
      .locator(
        'xpath=//input[@type="file" and @accept="image/*, video/*, .mkv, .flv"]'
      )
      .setInputFiles(filePaths);
    await this.page.waitForTimeout(3000);
  }
  //click post button
  async clickPostBtn(): Promise<void> {
    await this.page.getByRole("button", { name: "Đăng" }).click();
    await this.page.waitForTimeout(1000);
  }
  //view post detail
  async viewPostDetail(): Promise<void> {
    const viewPostDetais = this.page.getByRole("link", { name: "Vừa xong" });
    if ((await viewPostDetais.count()) > 1) {
      viewPostDetais.nth(1).click();
    } else viewPostDetais.click();
  }
  //select post background
  async selectPostBackground(): Promise<void> {
    //click icon post background
    await this.page.getByRole("button", { name: "Aa" }).click();
    //select background number 3
    await this.page
      .locator(".create-post__box__input-background-options > div:nth-child(3)")
      .click();
  }
  //view first media
  async viewDetailMedia() {
    await this.page.locator(".attachment__item > a").first().click();
  }
  //click to action sheet. Staging may have more than 1 actionsheet element
  async clickToSelectActionOfPost(): Promise<void> {
    const postActionSheet = this.page.locator(
      '//div[@class="post-item"][1]//div[@class="post-item__info media align-items-center"]/button'
    );
    if ((await postActionSheet.count()) > 1) {
      await postActionSheet.nth(1).scrollIntoViewIfNeeded();
      await postActionSheet.nth(1).click();
    } else {
      await postActionSheet.scrollIntoViewIfNeeded();
      await postActionSheet.click();
    }
  }
  //select user to tag
  async selectUserToTag(tagTotal: number): Promise<void> {
    await this.page
      .locator(
        'xpath=//button[contains(@class, "styles_actionButton__hanFv")][3]'
      )
      .click();

    // Lấy danh sách checkbox và check theo số lượng cần
    const checkboxes = this.page.getByRole("checkbox", { name: "checkbox" });

    for (let i = 0; i < tagTotal; i++) {
      await checkboxes.nth(i + 1).check();
    }

    // Click button Xong
    await this.page.getByRole("button", { name: "Xong" }).click();
  }

  //select option to delete post
  async selectDeleteOption(): Promise<void> {
    await this.page.getByText("Xóa bài viết").click();
  }
  //confirm yes to delete post
  async confirmYesToDeletePost(): Promise<void> {
    await this.page.getByRole("button", { name: "Có", exact: true }).click();
    await this.page.waitForTimeout(1000);
  }
  //mention user in post
  async mentionUserInPost(): Promise<void> {
    await this.page.getByRole("textbox").fill("Post mention user @auto");
    await this.page.getByText("G0Gapo Auto Test 03").click();
  }

  async clickToFirstUserMentionInPost(): Promise<void> {
    const userMention = this.page
      .getByRole("button", { name: "Post mention user Gapo Auto Test" })
      .first();
    // const userMention = this.page.locator('xpath=//div[@class="post-item"][1]//*[contains(@class,"user-mention")]').first();
    userMention.scrollIntoViewIfNeeded();
    await userMention.evaluate((el: HTMLElement) => el.click());
    await this.page.waitForTimeout(2000);
  }
  //select post ask me
  async clickToCreatePostAskMe(): Promise<void> {
    await this.page
      .locator(
        'xpath=//button[contains(@class, "styles_actionButton__hanFv")][6]'
      )
      .click();
  }
  //click to create pollvote
  async clickToCreatePostPollvote(): Promise<void> {
    await this.page
      .locator(
        'xpath=//button[contains(@class, "styles_actionButton__hanFv")][4]'
      )
      .click();
  }
  //input pollvote infor
  async inputPollvoteInfor(
    pollvoteQuestion: string,
    pollvoteOption: string[]
  ): Promise<void> {
    // Điền câu hỏi
    const questionInput = this.page.getByRole("textbox", { name: "Câu hỏi*" });
    await questionInput.click();
    await questionInput.fill(pollvoteQuestion);

    // Các lựa chọn
    for (let i = 0; i < pollvoteOption.length; i++) {
      if (i >= 2) {
        // Bấm nút Thêm lựa chọn với Lựa chọn 3 trở đi
        await this.page.getByRole("button", { name: "Thêm lựa chọn" }).click();
      }

      const placeholder = `Lựa chọn ${i + 1}`;
      const input = this.page.getByPlaceholder(placeholder);
      await input.click();
      await input.fill(pollvoteOption[i]);
    }
  }
  //select incognito checkbox
  async checkAllowIncognito(): Promise<void> {
    await this.page
      .getByRole("checkbox", { name: "Bình chọn ẩn danh" })
      .check();
  }
  //setup limit time to vote
  async limmitTimeForPollvote(): Promise<void> {
    await this.page
      .getByRole("checkbox", { name: "Giới hạn thời gian cuộc bình" })
      .check();
    await this.page.getByRole("textbox", { name: "17:18 27/06/" }).click();
    await this.page.getByRole("button", { name: "Ok" }).click();
    await this.page.getByRole("button", { name: "Tiếp tục" }).click();
  }

  async clickContinueCreatePollVote(): Promise<void> {
    await this.page.getByRole("button", { name: "Tiếp tục" }).click();
  }

  //delete latest post
  async deleteLatestPost(): Promise<void> {
    await this.clickToSelectActionOfPost();
    await this.selectDeleteOption();
    await this.confirmYesToDeletePost();
  }

  //delete all post
  async deleteAllPost(): Promise<void> {
    //get post list
    const postList = this.page.locator(
      'xpath=//div[@class="post-item"]//div[contains(@class, "post-item__content")]'
    );
    const totalPost = await postList.count();
    console.log("Total Post:", totalPost);
    //delete all post
    if (totalPost > 1) {
      for (let i = 0; i < totalPost; i++) {
        await this.deleteLatestPost();
        await this.page.waitForTimeout(500);
      }
    }
  }
}
