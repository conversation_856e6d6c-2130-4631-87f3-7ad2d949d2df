import { Page } from "@playwright/test";

export class NavigateAction {
  constructor(private page: Page) {}

  //open url
  async openUrl(): Promise<void> {
    await this.page.goto("/" + "login");
    await this.page.waitForTimeout(2000);
    console.log(">>> ENV from process.env =", process.env.ENV);
  }

  async switchLanguage(fromLang: string, toLang: string): Promise<void> {
    const langButton = this.page.getByRole("button", { name: fromLang });

    if (await langButton.isVisible()) {
      await langButton.click();
      await this.page.getByText(toLang).click();
    }
  }

  async clickToProfileIcon(): Promise<void> {
    await this.page.locator("#profile").click();
  }
  //goto user timeline
  async goToTimeLine(): Promise<void> {
    await this.page
      .locator(
        'xpath=//*[text()="Your profile" or text()="Trang cá nhân của bạn"]'
      )
      .click();
  }
  async gotoTask() {
    await this.page.goto('https://www.gapowork.vn');
    await this.page.locator('#task').getByRole('link', { name: 'Beta' }).click({ timeout: 5000 });
  }
  //click icon X
  async clickIconClear(){
    await this.page.locator('button.task-detail-control-btn').click();
  }
  
  async gotoChat() {
    await this.page.waitForSelector('a[href="/messenger"]', { timeout: 30000 });
    await this.page.locator('a[href="/messenger"]').click();
    await this.page.waitForTimeout(5000);
  }
  async acceptNotiPopup(): Promise<void> {
    const confirtmNotiPopup = this.page.getByRole("button", {
      name: "Bật thông báo",
    });
    if (await confirtmNotiPopup.isVisible()) {
      await confirtmNotiPopup.click();
    }
  }
  async clickbtnKP() {
    await this.page.locator('a.introjs-button.introjs-nextbutton.introjs-donebutton').click();
    await this.page.waitForTimeout(1000);
  }

  async goToSurvey(): Promise<void> {
    await this.page.getByRole("link", { name: "Khảo sát" }).click();
  }

  async goToCreatedSurvey(): Promise<void> {
    await this.page.goto("/" + "created-survey");
    await this.page.waitForTimeout(2000);
  }

  async closeNotiPopup(): Promise<void> {
    await this.page.waitForTimeout(3000);
    await this.page.locator("#desktop-notification > button").click();
  }

  //accept desktop noti popup

  //accept guide post
  async continueGuidelineOfPost(): Promise<void> {
    const continueGuidelineOfPost = this.page.getByRole("button", {
      name: "Tiếp theo",
    });
    if (await continueGuidelineOfPost.isVisible()) {
      await continueGuidelineOfPost.click();
      await this.page.getByRole("button", { name: "Đã hiểu" }).click();
    }
  }


}