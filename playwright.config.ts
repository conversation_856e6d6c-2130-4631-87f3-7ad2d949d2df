import { defineConfig, devices } from "@playwright/test";
import * as dotenv from "dotenv";
import path from "path";

const ENV = process.env.ENV || 'staging';
dotenv.config({ path: path.resolve(__dirname, `.env.${ENV}`) });

const baseURL = {
  prod: 'https://gapowork.vn/',
  staging: 'https://staging.gapowork.vn/',
  uat: 'https://uat.gapowork.vn/',
}[ENV];

// function storageStateFile(user: string) {
//   return `data/storage/${ENV}_${user}_storageState.json`;
// }

export default defineConfig({
  testDir: "./tests",
  timeout: 30 * 1000,
  expect: {
    timeout: 5000,
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  outputDir: "test-results/",
  reporter: [["html", { outputFolder: "playwright-report", open: "always" }]],

  use: {
    actionTimeout: 0,
    trace: "on-first-retry",
    screenshot: "only-on-failure",
    video: "on",
  },

  projects: [
    // // SETUP cho từng module
    // ...['post', 'calendar', 'survey'].map(user => ({
    //   name: `setup-${user}`,
    //   testMatch: [`tests/setup/${user}.setup.ts`],
    //   use: {
    //     ...devices['Desktop Chrome'],
    //     baseURL,
    //   },
    // })),

    // MODULES chạy 3 trình duyệt cho từng module
    ...['post', 'calendar', 'survey','auth', 'task'].flatMap((module, i) => {
      const moduleFile = `test-${module}.spec.ts`;
      console.log('module file:', moduleFile);
      return ['Desktop Chrome', 'Desktop Firefox', 'Desktop Safari'].map(deviceName => {
        const browser = deviceName.split(' ')[1].toLowerCase();
        return {
          name: `test-${ENV}-${browser}-${module}`,
          testMatch: [moduleFile],
          // dependencies: [`setup-${user}`],
          use: {
            ...devices[deviceName],
            baseURL,
          //  storageState: storageStateFile(user),
          },
        };
      });
    }),

    // // AUTH: kiểm tra đăng nhập độc lập, không phụ thuộc setup
    // ...['Desktop Chrome', 'Desktop Firefox', 'Desktop Safari'].map(deviceName => {
    //   const browser = deviceName.split(' ')[1].toLowerCase();
    //   return {
    //     name: `test-${ENV}-${browser}-auth`,
    //     testMatch: ['tests/verifyauth.spec.ts'],
    //     use: {
    //       ...devices[deviceName],
    //       baseURL,
    //     },
    //   };
    // }),
  ],
});

console.log("ENV =", process.env.ENV);

