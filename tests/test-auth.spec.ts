import { test, expect } from "@playwright/test";
import { getAccountAuth } from "../helpers/getAccountAuth";
import { AuthenAction } from "../pages/authen/authen-action";
import { NavigateAction } from "../pages/navigate/navigate-action";
import { AuthenVerify } from "../pages/authen/authen-verify";
import authendata from "../data/authen-data.json";

test.describe("@testlogin test login gapowork", () => {
  // test.describe.configure({ mode: "serial" });
  let authenAction: AuthenAction;
  let navigateAction: NavigateAction;
  let authenVerify: AuthenVerify;

  test.beforeEach(async ({ page }) => {
    authenAction = new AuthenAction(page);
    navigateAction = new NavigateAction(page);
    authenVerify = new AuthenVerify(page);
    await navigateAction.openUrl();
    await navigateAction.switchLanguage("English", "Vietnamese");
  });

  test("@loginbyemail login by email successfully", async ({ page }) => {
    const { email, password } = getAccountAuth().email_account;
    console.log(email);
    console.log(password);
    await authenAction.inputEmailAccount(email, password);
    await authenAction.clickContinueToLogin();
    await authenVerify.verifyLoginSuccessfully();
  });

  test("login by phone successfully", async ({ page }) => {
    const { phone_number, password } = getAccountAuth().phone_account;
    await authenAction.inputPhoneNumberAccount(phone_number, password);
    await authenAction.clickContinueToLogin();
    await authenVerify.verifyLoginSuccessfully();
  });

  test("login by identifier successfully", async ({ page }) => {
    const { company_name, identifier_code, password } =
      getAccountAuth().identifier_account;
    await authenAction.inputIdentifierAccount(
      company_name,
      identifier_code,
      password
    );
    await authenAction.clickLogin();
    await authenVerify.verifyLoginSuccessfully();
  });

  test("login by microsoft successfully", async ({ page }) => {
    const { microsoft_email, password } = getAccountAuth().microsoft_account;
    await authenAction.loginByMicrosoft(microsoft_email, password);
    await authenVerify.verifyLoginSuccessfully();
  });

  test("login by sso successfully", async ({ page }) => {
    const { sso_email, password } = getAccountAuth().sso_account;
    await authenAction.loginBySSO(sso_email, password);
    await authenVerify.verifyLoginSuccessfully();
  });

  test.only("change password", async ({ page }) => {
    //login
    const { email, password } = authendata.change_pass_account
    await authenAction.inputEmailAccount(email, password);
    await authenAction.clickContinueToLogin();
    await authenVerify.verifyLoginSuccessfully();
    //go to setting
    await navigateAction.clickToProfileIcon();
    await navigateAction.gotoSetting();
    await navigateAction.clickChangePass();
    //change password
    const newPass = await authenAction.changePassword();
    await authenAction.clickCloseOnAlertPopup();
    //logout
    await navigateAction.clickToProfileIcon();
    await authenAction.logout();
    //login with new password
    const currentEmail = authendata.change_pass_account.email;
    await authenAction.inputEmailAccount(currentEmail, newPass);
    await authenAction.clickContinueToLogin();
    await authenVerify.verifyLoginSuccessfully();
    // save file
    await authenAction.saveNewPasswordToFile(newPass);

  });

  test.afterEach(async ({ page }) => {
    await navigateAction.clickToProfileIcon();
    await authenAction.logout();
  });
});
