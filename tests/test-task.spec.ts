// tests/test-task.spec.ts
import { Page, test, BrowserContext } from "@playwright/test";
import { createStorageState } from "./setup/common-setup";
import { NavigateAction } from "../pages/navigate/navigate-action";
import { TaskAction } from "../pages/task/task-action";
import { TaskVerify } from "../pages/task/task-verify";
import taskData from "../data/task-data.json";

let page: Page;
let context: BrowserContext;
let nav: NavigateAction;
let action: TaskAction;
let verify: TaskVerify;

const ENV = process.env.ENV || "prod";
test.describe("Task Workflow", () => {
  test.beforeAll(async ({ browser }) => {
    // Tạo storage chung từ account task
    const storagePath = await createStorageState(
      browser,
      ENV,
      "task_account", // key account trong file {ENV}-accounts.json
      `${ENV}-task-storage-state.json`
    );

    // Mở context từ storage
    context = await browser.newContext({ storageState: storagePath });
    page = await context.newPage();
    nav = new NavigateAction(page);
    action = new TaskAction(page);
    verify = new TaskVerify(page);

    // Điều hướng 1 lần
    await nav.openUrl();
  });
  test.beforeEach(async () =>{
    await nav.gotoTask();
  });
  test.afterAll(async () => {
    await context.close();
  });

  test.only("@createProject Tạo Project", async () => {
    await action.clickIconProject1();
    await action.clickProjectName(taskData.projectName);
    await action.addMemberBtn();  
    await action.chooseMemberToProject();
    await action.closeProjectModal();
    await action.clickDoneButton();
    await action.clickSaveProject();
    await nav.continueGuidelineOfPost();
    await verify.verifyProjectVisible(taskData.projectName);
  });

  test("@createTaskList  Tạo danh sách công việc", async () => {
    await action.chooseProject(taskData.projectName, taskData.newProjectName);
    await action.clickIconProject2();
    await action.clickChooseTaskListOrFolder();
    await action.clickChooseTaskList();
    await action.addTaskList1(taskData.nameTaskList1);
    await action.clickSaveBtn();
    await nav.continueGuidelineOfPost();
    await verify.verifyTaskListVisible(taskData.nameTaskList1);
  });

  test("@createFolder Tạo thư mục", async () =>{
    await action.chooseProject(taskData.projectName, taskData.newProjectName);
    await action.clickIconProject2();
    await action.clickChooseTaskListOrFolder();
    await action.clickChooseFolder();
    await action.addFolder(taskData.nameFolder1);
    await action.clickSaveBtn();
    await nav.continueGuidelineOfPost();
    await verify.verifyFolderVisible(taskData.nameFolder1);
  });
  
  test("@createTask Tạo Task", async () => {
    await action.chooseProject(taskData.projectName,taskData.newProjectName);
    await action.chooseTaskList(taskData.nameTaskList1);
    await nav.continueGuidelineOfPost();
    await action.clickCreateTaskBtn();
    await action.clickCreateNewTaskBtn();
    await action.fillTaskTitle(taskData.nameTask);
    await action.clickSaveBtn();
    await verify.verifyTaskVisible(taskData.nameTask);
  });

  test("@editTask Chỉnh sửa Task", async () => {
    await action.chooseProject(taskData.projectName,taskData.newProjectName);
    await action.chooseTaskList(taskData.nameTaskList1);
    await nav.continueGuidelineOfPost();
    await action.chooseTask(taskData.nameTask);
    await action.clickThreeDotMenu();
    await action.clickViewDetailsBtn();
    await action.fillTaskTitle(taskData.newNameTask);
    await nav.clickIconClear();
    await verify.verifyNewTaskVisible(taskData.newNameTask);
  });

  test("@editTask Tạo bản sao Task", async () => {
    await action.chooseProject(taskData.projectName,taskData.newProjectName);
    await action.chooseTaskList(taskData.nameTaskList1);
    await nav.continueGuidelineOfPost();
    await action.chooseTask(taskData.nameTask);
    await action.clickThreeDotMenu();
    await action.clickDuplicateBtn();
    await nav.clickIconClear();
    await verify.verifyDuplicateTaskVisible(taskData.nameTask);
  });

  test("Thêm thành viên vào dự án", async () =>{
    await action.chooseProject(taskData.projectName, taskData.newProjectName);
    await action.clickProjectOptionBtn();
    await action.clickActionAdd();
    await action.chooseMemberToProject();
    await action.closeProjectModal();
    await action.clickDoneButton();
    await page.waitForTimeout(1000);
    await action.clickTaskListOrFolderOptionBtn(taskData.projectName);
    await action.clickActionAdd();
    await verify.verifyAddMemberVisible(taskData.projectName);
  });

  test("@duplicate Tạo bản sao DSCV", async ()=>{
    await action.chooseProject(taskData.projectName, taskData.newProjectName);
    await action.clickTaskListOrFolderOptionBtn(taskData.nameTaskList1);
    await action.clickActionDuplicate();
    await verify.verifyDuplicateTaskListVisible(taskData.nameTaskList1);
  });

  test("@editTask Chỉnh sửa Project", async () => {
    await action.chooseProject(taskData.projectName, taskData.newProjectName);
    await action.clickProjectOptionBtn();
    await action.clickActionEdit();
    await action.fillNewProjectName(taskData.newProjectName);
    await action.addMemberBtn();
    await action.chooseMemberToProject();
    await action.closeProjectModal();
    await action.clickDoneButton();
    await action.clickSaveProject();
    await verify.verifyEditProjectVisible(taskData.newProjectName);
  });

  test("Tạo task ở CVCT", async () => {
    await action.clickMyTask();
    await action.clickCreateTaskBtn();
    await action.fillTaskTitle(taskData.nameMyTask);
    await action.clickSaveBtn();
    await verify.verifyMyTaskVisible(taskData.nameMyTask);
  });

  test("Xóa task ở CVCT", async () => {
    await action.listTaskByMyTask(taskData.nameMyTask);
    await action.clickThreeDotMenu();
    await action.clickDeleteBtn();
    await verify.verifyMyTaskNotExist(taskData.nameMyTask);
  });

  test("@deleteProject Xoá Project", async () => {
    await action.chooseProject(taskData.projectName, taskData.newProjectName);
    await action.clickProjectOptionBtn();
    await action.clickDeleteProjectBtn();
    await verify.verifyProjectNotExist(taskData.projectName, taskData.newProjectName);
  });

  // test("Lưu trữ  project",async () =>{
  //   await action.chooseProject(taskData.projectName, taskData.newProjectName);
  //   await action.clickProjectOptionBtn();
  //   await action.clickActionArchived();
  //   await action.clickArchivedBtn();

  //   //await verify.verifyArchived();
  // });

  test("@collab Tạo nhóm collab", async () => {
    await nav.gotoChat();
    await action.clickEnterKeyTwice();
    await action.clickCreatGroupChat();
    await action.sellectUser();
    await nav.acceptNotiPopup();
    await action.clickDoneChat();
    await nav.clickbtnKP();
    await action.tabTask();
    await action.enableTaskCollab();
    await nav.continueGuidelineOfPost();
    await action.showAProject();
    await nav.gotoTask();
    await verify.verifyCollabProjectVisible(taskData.collabProjectName);
  });

  test("@collab Ẩn dự án", async() =>{
    await action.clickCollabProject(taskData.collabProjectName);
    await action.clickCollabProjectOption(taskData.collabProjectName);
    await action.clickActionnHideProject();
    await verify.verifyCollabProjecttNotExist(taskData.collabProjectName);
  });
});