import { Page, test } from "@playwright/test";
import { createStorageState } from "./setup/common-setup";
import { Context } from "vm";
import { NavigateAction } from "../pages/navigate/navigate-action";
import { SurveyAction } from "../pages/survey/survey-action";
import { SurveyVerify } from "../pages/survey/survey-verify";
import surveyData from "../data/survey-data.json";

let page: Page;
let context: Context;
let nav: NavigateAction;
let surveyAction: SurveyAction;
let surveyVerify: SurveyVerify;
const ENV = process.env.ENV || "prod";

test.describe("Survey Tests", () => {
  test.beforeAll(async ({ browser }) => {
    // Tạo storage chung
    const storagePath = await createStorageState(
      browser,
      ENV,
      "survey_account", // account key trong getAccountData()
      `${ENV}-survey-storage-state.json`
    );

    // Mở context từ storage
    context = await browser.newContext({ storageState: storagePath });
    page = await context.newPage();
    nav = new NavigateAction(page);
  });

  test.afterAll(async () => {
    await context.close();
  });

  test.beforeEach(async () => {
    await nav.openUrl();
    await nav.goToSurvey();
    await nav.closeNotiPopup();
    surveyAction = new SurveyAction(page);
    surveyVerify = new SurveyVerify(page);
  });

  test("@createsurvey no deadline", async () => {
    await surveyAction.createSurveyNoDeadline(
      surveyData.case1.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyVisible();
    await surveyAction.closeSuccessPopup();
  });

  test("@createsurveyWithDeadline with deadline", async () => {
    await surveyAction.createSurveyWithDeadline(
      surveyData.case2.deadlineDate,
      surveyData.case2.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyVisible();
    await surveyAction.closeSuccessPopup();
  });

  test("@createsurveyRecurring recurring survey", async () => {
    await surveyAction.createRecurringSurvey(
      surveyData.case3.participants,
      surveyData.new_survey.title
    );
    await surveyVerify.verifySurveyVisible();
    await surveyAction.closeSuccessPopup();
  });

  test("@editSurvey change type", async () => {
    await surveyAction.createRecurringSurvey(
      surveyData.case4.participants,
      surveyData.new_survey.title
    );
    await surveyAction.closeSuccessPopup();
    await surveyAction.editFirstSurveyType(surveyData.case4.newType);
    await surveyVerify.verifySurveyVisible();
  });

  test.only("@deleteSurvey delete first survey", async () => {
    await nav.goToCreatedSurvey();
    await surveyAction.deleteFirstSurvey();
    await surveyVerify.verifySurveyDeleted();
  });

  test.afterEach(async () => {
    if (test.info().title.includes("delete")) return;
    await nav.goToCreatedSurvey();
    // await surveyAction.cleanUpSurveys();
    await surveyAction.deleteFirstSurvey();
  });

  //  Case 6 Khảo sát định kỳ hàng ngày
await page.getByRole('link', { name: 'Khảo sát' }).click();
await page.getByRole('button', { name: 'Tạo khảo sát' }).click();
await page.getByRole('radio', { name: 'Định kỳ' }).check();
await page.getByRole('button', { name: 'Hàng ngày' }).click();
await page.getByTestId('popover').getByText('Hàng ngày').click();
await page.getByRole('button', { name: 'Nhắc trả lời trước 30 phút' }).click();
await page.getByText('Không nhắc trước').click();
await page.getByRole('button', { name: 'Thêm người' }).click();
await page.getByText('Trần Thị Huyền').click();
await page.getByRole('button', { name: 'Xong' }).click();
await page.getByRole('button', { name: 'Đoạn văn bản' }).click();
await page.getByRole('button', { name: 'Gửi khảo sát' }).click();
await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
await page.getByRole('button', { name: 'Xem khảo sát' }).click();
await expect(page.getByText('hang ngay').nth(1)).toBeVisible();
// Case 7 Khảo sát định kỳ hàng tuần
await page.locator('#feed').getByRole('link', { name: 'Beta' }).click();
await page.getByRole('link', { name: 'Khảo sát' }).click();
await page.getByRole('button', { name: 'Tạo khảo sát' }).click();
await page.getByRole('radio', { name: 'Định kỳ' }).check();
await page.getByRole('button', { name: 'Hàng ngày' }).click();
await page.getByText('Hàng tuần').click();
await page.getByRole('button', { name: 'T3' }).click();
await page.getByRole('button', { name: 'Nhắc trả lời trước 30 phút' }).click();
await page.getByText('Không nhắc trước').click();
await page.getByRole('button', { name: 'Thêm người' }).click();
await page.getByText('Trần Thị Huyền').click();
await page.getByRole('button', { name: 'Xong' }).click();
await page.getByRole('button', { name: 'Đoạn văn bản' }).click();
await page.getByRole('button', { name: 'Gửi khảo sát' }).click();
// Case 8 Khảo sát định kỳ hàng tháng
await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
await page.getByRole('button', { name: 'Xem khảo sát' }).click();
await expect(page.getByText('hang tuan').nth(1)).toBeVisible();
await page.locator('#feed').getByRole('link', { name: 'Beta' }).click();
await page.getByRole('link', { name: 'Khảo sát' }).click();
await page.getByRole('button', { name: 'Tạo khảo sát' }).click();
await page.getByRole('radio', { name: 'Định kỳ' }).check();
await page.getByRole('button', { name: 'Hàng ngày' }).click();
await page.getByText('Hàng tháng').click();
await page.getByRole('button', { name: 'Nhắc trả lời trước 30 phút' }).click();
await page.getByText('Không nhắc trước').click();
await page.getByRole('button', { name: 'Thêm người' }).click();
await page.getByText('Trần Thị Huyền').click();
await page.getByRole('button', { name: 'Xong' }).click();
await page.getByRole('button', { name: 'Đoạn văn bản' }).click();
await page.getByRole('button', { name: 'Gửi khảo sát' }).click();
await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
await page.getByRole('button', { name: 'Xem khảo sát' }).click();
await expect(page.getByText('hang thang').nth(1)).toBeVisible();
// Case 9 Khảo sát định kỳ hàng ngày sử dụng mẫu khảo sát
await page.locator('#feed').getByRole('link', { name: 'Beta' }).click();
await page.getByRole('link', { name: 'Khảo sát' }).click();
await page.getByRole('button', { name: 'Tạo khảo sát' }).click();
await page.getByRole('button', { name: 'Khảo sát mẫu' }).first().click();
await page.getByRole('button', { name: 'Tiền lương và phúc lợi' }).click();
await page.getByRole('button', { name: 'Sử dụng mẫu' }).click();
await page.getByRole('radio', { name: 'Định kỳ' }).check();
await page.getByRole('button', { name: 'Thêm người' }).click();
await page.locator('div > div:nth-child(9) > div').click();
await page.getByRole('button', { name: 'Xong' }).click();
await page.getByRole('button', { name: 'Nhắc trả lời trước 30 phút' }).click();
await page.getByText('Không nhắc trước').click();
await page.getByRole('button', { name: 'Gửi khảo sát' }).click();
await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
await page.getByRole('button', { name: 'Xem khảo sát' }).click();
await expect(page.getByText('Tiền lương và phúc lợi').nth(1)).toBeVisible();
// Case 10 Chỉnh sửa thêm câu hỏi của case 9
await page.getByRole('complementary').getByRole('button').nth(1).click();
await page.getByRole('button', { name: 'Đoạn văn bản' }).click();
await page.getByRole('button', { name: 'Sửa khảo sát' }).click();
await expect(page.getByRole('button', { name: 'Xem khảo sát' })).toBeVisible();
await page.getByRole('button', { name: 'Xem khảo sát' }).click();
await expect(page.getByText('Them cau hoi')).toBeVisible();
});
